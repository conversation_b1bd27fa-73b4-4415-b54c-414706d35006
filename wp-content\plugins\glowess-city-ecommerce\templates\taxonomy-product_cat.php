<?php
/**
 * <PERSON>gori sayfası template'i - Şehir bazlı filtreleme ile
 */

// Şehir seçimi kontrolü
$selected_city = glowess_get_selected_city();
$category = get_queried_object();

get_header('shop'); ?>

<div class="glowess-category-page">
    <?php if ($selected_city): 
        $city = get_post($selected_city);
        $city_name = $city ? $city->post_title : 'Seçili Şehir';
    ?>
        <!-- Şehir bilgisi -->
        <div class="glowess-city-category-header">
            <div class="container">
                <div class="city-category-info">
                    <h1 class="category-title">
                        <?php echo esc_html($city_name); ?> - <?php echo esc_html($category->name); ?>
                    </h1>
                    <p class="category-description">
                        <?php echo esc_html($city_name); ?> şehrindeki <?php echo esc_html($category->name); ?> kategorisindeki ürünler
                    </p>
                    <div class="city-change-option">
                        <a href="#" class="city-selector-trigger">📍 Şehir Değiştir</a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Şehir seçilmemiş -->
        <div class="glowess-no-city-category">
            <div class="container">
                <h1><?php echo esc_html($category->name); ?> Kategorisi</h1>
                <div class="city-selection-prompt">
                    <p><strong>Bu kategorideki ürünleri görmek için şehrinizi seçin</strong></p>
                    <a href="#" class="city-selector-trigger glowess-city-btn">Şehir Seç</a>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- WooCommerce içeriği -->
    <div class="container">
        <?php
        /**
         * Hook: woocommerce_before_main_content
         */
        do_action('woocommerce_before_main_content');
        ?>

        <header class="woocommerce-products-header">
            <?php if (apply_filters('woocommerce_show_page_title', true)): ?>
                <h1 class="woocommerce-products-header__title page-title"><?php woocommerce_page_title(); ?></h1>
            <?php endif; ?>

            <?php
            /**
             * Hook: woocommerce_archive_description
             */
            do_action('woocommerce_archive_description');
            ?>
        </header>

        <?php if (woocommerce_product_loop()): ?>
            <?php
            /**
             * Hook: woocommerce_before_shop_loop
             */
            do_action('woocommerce_before_shop_loop');

            woocommerce_product_loop_start();

            if (wc_get_loop_prop('is_shortcode')) {
                $columns = wc_get_loop_prop('columns');
            } else {
                $columns = wc_get_default_products_per_row();
            }

            while (have_posts()) {
                the_post();

                /**
                 * Hook: woocommerce_shop_loop
                 */
                do_action('woocommerce_shop_loop');

                wc_get_template_part('content', 'product');
            }

            woocommerce_product_loop_end();

            /**
             * Hook: woocommerce_after_shop_loop
             */
            do_action('woocommerce_after_shop_loop');
            ?>
        <?php else: ?>
            <?php
            /**
             * Hook: woocommerce_no_products_found
             */
            do_action('woocommerce_no_products_found');
            ?>
        <?php endif; ?>

        <?php
        /**
         * Hook: woocommerce_after_main_content
         */
        do_action('woocommerce_after_main_content');
        ?>
    </div>
</div>

<style>
.glowess-city-category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 0;
    margin-bottom: 30px;
}

.city-category-info {
    text-align: center;
}

.category-title {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.category-description {
    font-size: 1.1rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

.city-change-option a {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.city-change-option a:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

.glowess-no-city-category {
    background: #f8f9fa;
    padding: 60px 0;
    text-align: center;
    margin-bottom: 30px;
}

.city-selection-prompt {
    margin-top: 20px;
}

.glowess-city-btn {
    background: #667eea;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.glowess-city-btn:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}
</style>

<?php get_footer('shop'); ?>
